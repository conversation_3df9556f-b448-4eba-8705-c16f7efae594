<template>
  <div class="flex flex-col min-h-screen bg-gray-100">
    <!-- 校准过程中的全局loading遮罩 -->
    <van-overlay :show="isCalibrating" z-index="3000">
      <van-loading vertical class="overlay-content">
        <template #icon>
          <van-icon name="setting-o" size="30" />
        </template>
        正在校准测试，请稍候...
      </van-loading>
    </van-overlay>

    <div class="p-4 bg-white h-full flex flex-col">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold">通道管理</h2>
        <van-button icon="arrow-left" size="small" @click="goBack" plain />
      </div>
      <van-form @submit="onAddChannel" class="mb-4 flex flex-wrap gap-2">
        <van-field label="通道名称" required class="flex-1 min-w-[160px]">
          <template #input>
            <input
              keyboard="true"
              v-model="newChannel.name"
              placeholder="请输入通道名称"
              type="text"
              style="border: none"
            /> </template
        ></van-field>
        <van-field
          v-model="newChannel.channel_no"
          label="通道编号"
          placeholder="请输入通道编号"
          required
          class="flex-1 min-w-[160px]"
          readonly
          clickable
          @click="onFocusField(newChannel, 'channel_no', 'text')"
        />
        <van-field
          v-model.number="newChannel.max_capacity"
          label="最大容量"
          placeholder="请输入最大容量"
          required
          class="flex-1 min-w-[160px]"
          readonly
          clickable
          @click="onFocusField(newChannel, 'max_capacity', 'number')"
        />
        <van-field
          v-model.number="newChannel.warn_capacity"
          label="警告容量"
          placeholder="请输入警告容量"
          required
          class="flex-1 min-w-[160px]"
          readonly
          clickable
          @click="onFocusField(newChannel, 'warn_capacity', 'number')"
        />
        <van-field
          v-model="newChannel.mode_name"
          label="模式"
          required
          class="flex-1 min-w-[160px]"
          readonly
          clickable
          @click="showModePicker = true"
          placeholder="请选择模式"
        />
        <van-popup v-model:show="showModePicker" position="bottom">
          <van-picker
            :columns="modeOptions"
            @confirm="onModeConfirm"
            @cancel="showModePicker = false"
          />
        </van-popup>
        <div class="w-full flex justify-end mt-2">
          <van-button round type="success" native-type="submit">新增通道</van-button>
        </div>
      </van-form>
      <div class="overflow-x-auto flex-1">
        <table class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm table-fixed">
          <thead>
            <tr class="bg-gray-50">
              <th class="py-2 px-3 text-center w-24">名称</th>
              <th class="py-2 px-3 text-center w-24">编号</th>
              <th class="py-2 px-3 text-center w-46">最大容量</th>
              <th class="py-2 px-3 text-center w-46">警告容量</th>
              <th class="py-2 px-3 text-center w-46">出货模式</th>
              <th class="py-2 px-3 text-center w-46">出货校准</th>
              <th class="py-2 px-3 text-center w-46">解决操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="channel in channels" :key="channel.id" class="border-t">
              <td class="py-1 px-2 text-center">
                <van-field
                  v-show="editId === channel.id"
                  v-model="editChannel.name"
                  placeholder="名称"
                  class="w-full"
                  input-align="center"
                >
                  <template #input>
                    <input
                      class="w-full text-center"
                      keyboard="true"
                      v-model="editChannel.name"
                      placeholder="请输入通道名称"
                      type="text"
                      style="border: none"
                    /> </template
                ></van-field>
                <span v-show="editId !== channel.id">{{ channel.name }}</span>
              </td>
              <td class="py-1 px-2 text-center">
                <van-field
                  v-if="editId === channel.id"
                  v-model="editChannel.channel_no"
                  placeholder="编号"
                  class="w-full"
                  readonly
                  clickable
                  input-align="center"
                  @click="onFocusField(editChannel, 'channel_no', 'text')"
                />
                <span v-else>{{ channel.channel_no }}</span>
              </td>
              <td class="py-1 px-2 text-center">
                <van-field
                  v-if="editId === channel.id"
                  v-model.number="editChannel.max_capacity"
                  placeholder="最大容量"
                  class="w-full"
                  readonly
                  input-align="center"
                  clickable
                  @click="onFocusField(editChannel, 'max_capacity', 'number')"
                />
                <span v-else>{{ channel.max_capacity }}</span>
              </td>
              <td class="py-1 px-2 text-center">
                <van-field
                  v-if="editId === channel.id"
                  v-model.number="editChannel.warn_capacity"
                  placeholder="警告容量"
                  class="w-full"
                  readonly
                  input-align="center"
                  clickable
                  @click="onFocusField(editChannel, 'warn_capacity', 'number')"
                />
                <span v-else>{{ channel.warn_capacity }}</span>
              </td>
              <td class="py-1 px-2 text-center">
                <template v-if="editId === channel.id">
                  <van-popup v-model:show="showEditModePicker" position="bottom">
                    <van-picker
                      :columns="modeOptions"
                      @confirm="onEditModeConfirm"
                      @cancel="showEditModePicker = false"
                    />
                  </van-popup>
                </template>
                <div class="w-full text-center" @click="showEditModePicker = true">
                  {{ modeOptions.find((option) => option.value === channel.mode)?.text }}
                </div>
              </td>
              <td class="py-1 px-2 text-center">
                <van-button size="small" type="warning" @click="onCalibrate(channel)"
                  >出货校准</van-button
                >
              </td>
              <td class="py-1 px-2 text-center">
                <div v-if="editId !== channel.id" class="flex gap-1 justify-center">
                  <van-button size="small" type="primary" @click="onEdit(channel)">编辑</van-button>
                  <van-button size="small" type="danger" @click="onDeleteChannel(channel)"
                    >删除</van-button
                  >
                </div>
                <div v-else class="flex gap-1 justify-center">
                  <van-button size="small" type="success" @click="onUpdateChannel">保存</van-button>
                  <van-button size="small" @click="cancelEdit">取消</van-button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 出货校准对话框 -->
    <van-dialog
      v-model:show="showCalibrateDialog"
      title="出货校准"
      width="600px"
      :show-cancel-button="true"
      :show-confirm-button="true"
      cancel-button-text="取消"
      confirm-button-text="确认"
      @cancel="onCalibrateCancel"
      @confirm="onCalibrateConfirm"
    >
      <div class="p-6 space-y-4">
        <div class="text-center text-gray-600 mb-4">请先放入杯子后 依次点击测试1、测试2</div>

        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <van-button
              type="primary"
              size="large"
              :disabled="calibrateData.test1Done || isCalibrating"
              :loading="isCalibrating && !calibrateData.test1Done"
              @click="onTest1"
              class="flex-1 mr-2"
            >
              {{ calibrateData.test1Done ? '测试1已完成' : '测试1' }}
            </van-button>
            <div class="text-right min-w-[120px]">
              <span class="text-sm text-gray-500">实际重量:</span>
              <div class="font-semibold text-lg">
                {{ calibrateData.test1Done ? calibrateData.test1Weight + 'g' : '--' }}
              </div>
            </div>
          </div>

          <div class="flex items-center justify-between">
            <van-button
              type="primary"
              size="large"
              :disabled="!calibrateData.test1Done || calibrateData.test2Done || isCalibrating"
              :loading="isCalibrating && calibrateData.test1Done && !calibrateData.test2Done"
              @click="onTest2"
              class="flex-1 mr-2"
            >
              {{ calibrateData.test2Done ? '测试2已完成' : '测试2' }}
            </van-button>
            <div class="text-right min-w-[120px]">
              <span class="text-sm text-gray-500">实际重量:</span>
              <div class="font-semibold text-lg">
                {{ calibrateData.test2Done ? calibrateData.test2Weight + 'g' : '--' }}
              </div>
            </div>
          </div>
        </div>

        <div v-if="currentCalibrateChannel" class="text-center text-sm text-gray-500 mt-4">
          当前校准通道: {{ currentCalibrateChannel.name }} ({{
            currentCalibrateChannel.channel_no
          }})
        </div>
      </div>
    </van-dialog>

    <!-- 数字键盘 -->
    <van-number-keyboard
      :show="show"
      theme="custom"
      extra-key="."
      z-index="9999"
      close-button-text="完成"
      @blur="onKeyboardBlur"
      @input="onInput"
      @delete="onDelete"
    />
    <!-- 中文键盘 -->
    <div
      v-if="showKeyboardZh"
      class="scale-content"
      :style="{
        transform: `scale(0.85)`,
        width: '1204px',
        height: '100px',
        transformOrigin: 'center bottom',
        translate: '-50%',
      }"
    >
      <keyboardZh
        :transitionTime="'0.5s'"
        :maxQuantify="8"
        @clickKey="clickKey"
        float
        :manyDict="manyDict"
        :singleDict="singleDict"
        @clickNumber="clickNumber"
        :blurHide="false"
      ></keyboardZh>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import {
  getChannels,
  addChannel,
  deleteChannel,
  updateChannel,
  type channelRes,
  type channelReq,
} from '../api/channel'
import { showConfirmDialog, showToast } from 'vant'
import router from '@/router'
import { keyboardZh } from '@/components/virtual-keyboard'
import { testChannelWeight, confirmCalibration } from '../api/send'

const show = ref(false)
const channels = ref<channelRes[]>([])
const newChannel = reactive<Omit<channelReq, 'id'>>({
  channel_no: '',
  max_capacity: 0,
  mode: '',
  mode_name: '',
  name: '',
  warn_capacity: 0,
})

const editId = ref<number | null>(null)
const editChannel = reactive<Partial<channelRes>>({})
const showKeyboardZh = ref(false)
const manyDict = ref('dict/chowder.json')
const singleDict = ref('dict/baseDict.json')
// 数字键盘相关状态
const currentFieldRef = ref<any>(null)
const currentFieldType = ref<string>('')
const currentFieldKey = ref<string>('')

const fetchChannels = async () => {
  channels.value = await getChannels()
  showKeyboardZhFunc()
}

const showKeyboardZhFunc = () => {
  nextTick(() => {
    showKeyboardZh.value = true
  })
}

const onAddChannel = async () => {
  await addChannel({ ...newChannel })
  Object.assign(newChannel, {
    channel_no: '',
    max_capacity: 0,
    mode: '',
    mode_name: '',
    name: '',
    warn_capacity: 0,
  })
  await fetchChannels()
  showToast({ message: '新增成功' })
}

const onDeleteChannel = async (channel: channelRes) => {
  showConfirmDialog({
    title: '确认删除',
    message: `确定要删除通道「${channel.name}」吗？`,
  })
    .then(async () => {
      await deleteChannel(channel)
      await fetchChannels()
      showToast({ message: '删除成功' })
    })
    .catch(() => {})
}

const onEdit = (channel: channelRes) => {
  editId.value = channel.id
  Object.assign(editChannel, channel)
}

const onUpdateChannel = async () => {
  if (editId.value !== null) {
    await updateChannel(editChannel as channelRes)
    editId.value = null
    await fetchChannels()
    showToast({ message: '修改成功' })
  }
}

const cancelEdit = () => {
  editId.value = null
}

const modeOptions = [
  { text: '普通', value: 'Normal' },
  { text: '合并', value: 'Combine' },
  { text: '回吸', value: 'Suck_back' },
]
const showModePicker = ref(false)
const showEditModePicker = ref(false)

// 校准相关状态
const showCalibrateDialog = ref(false)
const currentCalibrateChannel = ref<channelRes | null>(null)
const isCalibrating = ref(false) // 校准过程中的loading状态
const calibrateData = reactive({
  test1Weight: 0,
  test2Weight: 0,
  test1Done: false,
  test2Done: false,
})
const onModeConfirm = ({ selectedValues, selectedOptions }) => {
  newChannel.mode = selectedValues[0]
  newChannel.mode_name = selectedOptions[0].text
  showModePicker.value = false
}

const onEditModeConfirm = ({ selectedValues, selectedOptions }) => {
  console.log(selectedValues)
  editChannel.mode = selectedValues[0]
  editChannel.mode_name = selectedOptions[0].text
  showEditModePicker.value = false
}

// 数字键盘事件处理
const onInput = (val: string) => {
  if (!currentFieldRef.value || !currentFieldKey.value) return

  const currentValue = String(currentFieldRef.value[currentFieldKey.value] || '')

  // 防止多余的0
  const oldVal = currentValue === '0' ? '' : currentValue

  // 限制小数点只出现一次
  if (val === '.' && oldVal.includes('.')) return

  // 不允许以小数点开头
  if (val === '.' && oldVal === '') return

  const newVal = oldVal + val
  currentFieldRef.value[currentFieldKey.value] =
    currentFieldType.value === 'number' ? Number(newVal) : newVal
}

const onDelete = () => {
  if (!currentFieldRef.value || !currentFieldKey.value) return

  const currentValue = String(currentFieldRef.value[currentFieldKey.value] || '')
  const newVal = currentValue.slice(0, -1)
  currentFieldRef.value[currentFieldKey.value] =
    currentFieldType.value === 'number' ? Number(newVal) || 0 : newVal
}

const onKeyboardBlur = () => {
  show.value = false
  currentFieldRef.value = null
  currentFieldType.value = ''
  currentFieldKey.value = ''
}

const onFocusField = (fieldRef: any, fieldKey: string, fieldType: string = 'text') => {
  currentFieldRef.value = fieldRef
  currentFieldKey.value = fieldKey
  currentFieldType.value = fieldType
  show.value = true
}

const goBack = () => {
  router.back()
}

// 校准相关函数
const onCalibrate = (channel: channelRes) => {
  currentCalibrateChannel.value = channel
  // 重置校准数据
  Object.assign(calibrateData, {
    test1Weight: 0,
    test2Weight: 0,
    test1Done: false,
    test2Done: false,
  })
  showCalibrateDialog.value = true
}

const onTest1 = async () => {
  if (!currentCalibrateChannel.value || isCalibrating.value) return

  isCalibrating.value = true
  try {
    const weight = await testChannelWeight(currentCalibrateChannel.value.channel_no)
    calibrateData.test1Weight = weight
    calibrateData.test1Done = true
    showToast({ message: '测试1完成' })
  } catch (error) {
    showToast({ message: '测试1失败', type: 'fail' })
  } finally {
    isCalibrating.value = false
  }
}

const onTest2 = async () => {
  if (!currentCalibrateChannel.value || isCalibrating.value || !calibrateData.test1Done) return

  isCalibrating.value = true
  try {
    const weight = await testChannelWeight(currentCalibrateChannel.value.channel_no)
    calibrateData.test2Weight = weight
    calibrateData.test2Done = true
    showToast({ message: '测试2完成' })
  } catch (error) {
    showToast({ message: '测试2失败', type: 'fail' })
  } finally {
    isCalibrating.value = false
  }
}

const onCalibrateCancel = () => {
  showCalibrateDialog.value = false
  currentCalibrateChannel.value = null
}

const onCalibrateConfirm = async () => {
  if (!currentCalibrateChannel.value) return
  if (!calibrateData.test1Done || !calibrateData.test2Done) {
    showToast({ message: '请先完成两次测试', type: 'fail' })
    return
  }

  try {
    const success = await confirmCalibration(
      currentCalibrateChannel.value.channel_no,
      calibrateData.test1Weight,
      calibrateData.test2Weight,
    )

    if (success) {
      showToast({ message: '校准完成' })
      showCalibrateDialog.value = false
      currentCalibrateChannel.value = null
    } else {
      showToast({ message: '校准失败', type: 'fail' })
    }
  } catch (error) {
    showToast({ message: '校准失败', type: 'fail' })
  }
}

//点击键盘的值
const clickKey = (key) => {
  console.log('key-->>', key)
}
//点击键盘时数字的值
const clickNumber = (key) => {
  // console.log("key-->>",key);
}

onMounted(() => {
  fetchChannels()
})
</script>
<style scoped>
.scale-content {
  position: absolute;
  bottom: 2px;
  left: 50%;
  z-index: 9999;
}

.overlay-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
  font-size: 16px;
}
</style>
